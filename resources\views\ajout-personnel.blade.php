@extends($userTheme ?? 'theme')

@section('contenu')

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="mb-sm-0">{{ __('message.Add Staff') }}</h4>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h4 class="card-title mb-0">{{ __('message.Add Staff') }}</h4>
        </div>
        <div class="card-body">
            @if (session('success'))
                <div class="alert alert-success">
                    {{ session('success') }}
                </div>
            @endif
            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif

            <form action="{{ route('personnel.store') }}" method="POST">
                @csrf

                <div class="mb-3">
                    <label>{{ __('message.Last Name') }}</label>
                    <input type="text" class="form-control" name="nom" value="{{ old('nom') }}" required>
                </div>

                <div class="mb-3">
                    <label>{{ __('message.First Name') }}</label>
                    <input type="text" class="form-control" name="prenom" value="{{ old('prenom') }}" required>
                </div>

                <div class="mb-3">
                    <label>{{ __('message.Email_staff') }}</label>
                    <input type="email" class="form-control" name="email" value="{{ old('email') }}" required>
                </div>

                <div class="mb-3">
                    <label>{{ __('message.Password') }}</label>
                    <input type="password" class="form-control" name="password" required>
                </div>

                <div class="mb-3">
                    <label>{{ __('message.Confirm Password') }}</label>
                    <input type="password" class="form-control" name="password_confirmation" required>
                </div>

                <div class="mb-3">
                    <label>{{ __('message.Company_staff') }}</label>
                    <select name="poste" class="form-select" required>
                        <option value="">{{ __('message.Select a position') }}</option>
                        @foreach(['Développeur Frontend', 'Développeur Backend', 'Chef de projet', 'Designer UI/UX', 'Testeur QA', 'DevOps', 'Scrum Master', 'Concepteur'] as $poste)
                            <option value="{{ $poste }}" {{ old('poste') == $poste ? 'selected' : '' }}>{{ $poste }}</option>
                        @endforeach
                    </select>
                </div>

                <button type="submit" class="btn btn-primary">{{ __('message.Add_staff') }}</button>
            </form>
        </div>
    </div>
</div>

@endsection


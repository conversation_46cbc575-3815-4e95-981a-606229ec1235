@extends($userTheme ?? 'theme')

@section('contenu')

<div class="container-fluid">

    @if(session('message'))
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer"></button>
        </div>
    @endif

    <div class="card mt-3">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title mb-0">{{ __('message.Staff List') }}</h4>
            <a href="/ajoutpersonnel" class="btn btn-success">{{ __('message.Add_personnel') }}</a>
        </div>
        <div class="card-body">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>{{ __('message.ID') }}</th>
                        <th>{{ __('message.User') }}</th>
                        <th>{{ __('message.Email_list') }}</th>
                        <th>{{ __('message.Position') }}</th>
                        <th>{{ __('message.Actions_personnel') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($data as $user)
                        <tr>
                            <td>{{ $user->id }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    {!! $user->getAvatarHtml('sm', 'me-2') !!}
                                    <div>
                                        <div class="fw-semibold">{{ $user->name }}</div>
                                        <small class="text-muted">{{ ucfirst($user->role) }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>{{ $user->email }}</td>
                            <td>
                                <span class="badge bg-info-subtle text-info">
                                    {{ $user->poste ?? 'N/A' }}
                                </span>
                            </td>
                            <td>
                                <div class="d-flex gap-1">
                                    <a href="/modifier-personnel/{{ $user->id }}" class="btn btn-warning btn-sm">
                                        <i class="ri-edit-line me-1"></i>{{ __('message.Edit_personnel') }}
                                    </a>
                                    <form action="/delete-personnel/{{ $user->id }}" method="POST" style="display:inline;">
                                        @csrf
                                        @method('DELETE')
                                        <button onclick="return confirm('{{ __('message.Confirm delete personnel') }}')" class="btn btn-danger btn-sm">
                                            <i class="ri-delete-bin-line me-1"></i>{{ __('message.Delete_personnel') }}
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
</div>

@endsection

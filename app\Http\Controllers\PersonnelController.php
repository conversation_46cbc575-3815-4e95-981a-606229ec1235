<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class PersonnelController extends Controller
{
    // Affiche la liste des personnels
    public function index()
    {
        // Si l'utilisateur est une entreprise, montrer seulement les membres qu'il a ajoutés
        if (auth()->user()->role == 'entreprise') {
            $data = User::where('role', 'membre')
                      ->where('entreprise_id', auth()->id())
                      ->get();
        } else {
            // Pour les admins, montrer tous les membres
            $data = User::where('role', 'membre')->get();
        }

        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('liste-personnel', compact('data'))->with('userTheme', $theme);
    }

    // Affiche le formulaire d'ajout
    public function create()
    {
        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('ajout-personnel')->with('userTheme', $theme);
    }

    // Enregistre un nouveau personnel
    public function store(Request $request)
    {
        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'prenom' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'poste' => 'required|string|max:255',
            'password' => 'required|string|min:6|confirmed',
        ]);

        User::create([
            'name' => $validated['nom'] . ' ' . $validated['prenom'],
            'email' => $validated['email'],
            'password' => Hash::make($validated['password']),
            'poste' => $validated['poste'],
            'role' => 'membre', // Ajouté correctement
            'entreprise_id' => auth()->id(), // Ajouter l'ID de l'utilisateur connecté comme entreprise_id
        ]);

        return redirect('/liste-personnel')->with('message', 'Personnel ajouté avec succès.');
    }

    // Affiche le formulaire de modification
    public function edit($id)
    {
        $data = User::findOrFail($id);

        // Vérifier si l'utilisateur est autorisé à modifier ce membre
        // Si c'est une entreprise, elle ne peut modifier que les membres qu'elle a ajoutés
        if (auth()->user()->role == 'entreprise' && $data->entreprise_id != auth()->id()) {
            return redirect('/liste-personnel')
                ->with('error', 'Vous n\'êtes pas autorisé à modifier ce membre.');
        }

        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('modifier-personnel', compact('data'))->with('userTheme', $theme);
    }

    // Met à jour un personnel existant
    public function update(Request $request, $id)
    {
        $validated = $request->validate([
            'nom' => 'required|string|max:255',
            'email' => "required|email|unique:users,email,{$id}",
            'poste' => 'required|string|max:255',
        ]);

        $user = User::findOrFail($id);

        // Vérifier si l'utilisateur est autorisé à modifier ce membre
        // Si c'est une entreprise, elle ne peut modifier que les membres qu'elle a ajoutés
        if (auth()->user()->role == 'entreprise' && $user->entreprise_id != auth()->id()) {
            return redirect('/liste-personnel')
                ->with('error', 'Vous n\'êtes pas autorisé à modifier ce membre.');
        }

        $user->name = $validated['nom'];
        $user->email = $validated['email'];
        $user->poste = $validated['poste'];
        $user->save();

        return redirect('/liste-personnel')->with('message', 'Personnel modifié avec succès.');
    }

    // Supprime un personnel
    public function destroy($id)
    {
        $personnel = User::findOrFail($id);

        // Vérifier si l'utilisateur est autorisé à supprimer ce membre
        // Si c'est une entreprise, elle ne peut supprimer que les membres qu'elle a ajoutés
        if (auth()->user()->role == 'entreprise' && $personnel->entreprise_id != auth()->id()) {
            return redirect('/liste-personnel')
                ->with('error', 'Vous n\'êtes pas autorisé à supprimer ce membre.');
        }

        $personnel->delete();

        return redirect('/liste-personnel')->with('message', 'Personnel supprimé avec succès.');
    }
}

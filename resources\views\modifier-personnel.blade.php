@extends($userTheme ?? 'theme')

@section('contenu')

<div class="container-fluid">
    @if(session('message'))
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer"></button>
        </div>
    @endif

    <div class="card">
        <div class="card-header">
            <h4 class="card-title mb-0">Modifier un Personnel</h4>
        </div>
        <div class="card-body">
            <form action="{{ route('personnel.update', $data->id) }}" method="POST">
                @csrf
                @method('PUT')

                <div class="mb-3">
                    <label>Nom</label>
                    <input type="text" class="form-control" name="nom" value="{{ old('nom', $data->name) }}">
                </div>

                <div class="mb-3">
                    <label>Email</label>
                    <input type="email" class="form-control" name="email" value="{{ old('email', $data->email) }}">
                </div>

                <div class="mb-3">
                    <label>Poste</label>
                    <select name="poste" class="form-select">
                        <option value="">Sélectionner un poste</option>
                        @foreach(['Développeur Frontend', 'Développeur Backend', 'Chef de projet', 'Designer UI/UX', 'Testeur QA', 'DevOps', 'Scrum Master', 'Concepteur'] as $poste)
                            <option value="{{ $poste }}" {{ old('poste', $data->poste) == $poste ? 'selected' : '' }}>
                                {{ $poste }}
                            </option>
                        @endforeach
                    </select>
                </div>

                <button type="submit" class="btn btn-primary">Modifier</button>
            </form>
        </div>
    </div>
</div>

@endsection
